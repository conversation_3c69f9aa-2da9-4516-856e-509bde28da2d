<template>
  <div class="certificate-template-page">
    <!-- 顶部统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :xs="24" :sm="12" :md="6" v-for="card in stats" :key="card.label">
        <el-card :class="['stat-card', card.bgClass]">
          <div class="stat-card-inner">
            <el-icon :class="['stat-icon', card.iconClass]">
              <component :is="card.icon" />
            </el-icon>
            <div>
              <div class="stat-num">{{ card.value }}</div>
              <div class="stat-label">{{ card.label }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <!-- 筛选区 -->
    <el-row :gutter="12" class="filter-row" align="middle" style="margin: 24px 0 12px 0">
      <el-col :span="4" :xs="24" :sm="6">
        <el-select v-model="filter.type" placeholder="全部类型" clearable style="width: 100%">
          <el-option label="全部类型" value="" />
          <el-option label="培训证书" value="training" />
          <el-option label="结业证书" value="completion" />
          <el-option label="技能证书" value="skill" />
        </el-select>
      </el-col>
      <el-col :span="4" :xs="24" :sm="6">
        <el-select v-model="filter.status" placeholder="全部状态" clearable style="width: 100%">
          <el-option label="全部状态" value="" />
          <el-option label="启用中" value="active" />
          <el-option label="已停用" value="inactive" />
          <el-option label="草稿" value="draft" />
        </el-select>
      </el-col>
      <el-col :span="6" :xs="24" :sm="8">
        <el-input v-model="filter.keyword" placeholder="搜索模板名称..." clearable />
      </el-col>
      <el-col :span="2" :xs="12" :sm="2">
        <el-button type="primary" @click="onFilter" style="width: 100%">筛选</el-button>
      </el-col>
    </el-row>
    <!-- 数据表格 -->
    <el-table
      :data="tableData"
      border
      stripe
      class="template-table"
      style="width: 100%"
      v-loading="loading"
    >
      <el-table-column prop="name" label="模板名称" min-width="160" />
      <el-table-column prop="type" label="证书类型" min-width="100">
        <template #default="scope">
          {{ certificateTypeMap[scope.row.type] || scope.row.type }}
        </template>
      </el-table-column>
      <el-table-column prop="course_name" label="适用课程" min-width="140" />
      <el-table-column prop="creator_name" label="创建人" min-width="80" />
      <el-table-column prop="status" label="状态" min-width="80">
        <template #default="scope">
          <el-tag
            :type="
              scope.row.status === 'active'
                ? 'success'
                : scope.row.status === 'draft'
                  ? 'warning'
                  : 'info'
            "
          >
            {{ templateStatusMap[scope.row.status] || scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" min-width="120" />
      <el-table-column label="操作" min-width="180">
        <template #default="scope">
          <el-button size="small" @click="onEdit(scope.row)">编辑</el-button>
          <el-button size="small" @click="onPreview(scope.row)">预览</el-button>
          <el-button
            v-if="scope.row.status === 'active'"
            size="small"
            type="danger"
            @click="onDisable(scope.row)"
            :loading="statusLoading[scope.row.id]"
            >停用</el-button
          >
          <el-button
            v-else
            size="small"
            type="primary"
            @click="onEnable(scope.row)"
            :loading="statusLoading[scope.row.id]"
            >启用</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.size"
      :page-sizes="[10, 20, 50, 100]"
      :total="pagination.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-top: 20px; justify-content: center"
    />
    <!-- 编辑证书模板抽屉 -->
    <AddCertificateTemplate
      v-model:visible="editDrawerVisible"
      :edit-data="currentEditRow"
      @success="handleEditSuccess"
    />
    <!-- 预览证书抽屉 -->
    <PreviewCertificate v-model:visible="previewDrawerVisible" :data="currentPreviewRow" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close, Document, EditPen, Tickets } from '@element-plus/icons-vue'
import AddCertificateTemplate from './components/AddCertificateTemplate.vue'
import PreviewCertificate from './components/PreviewCertificate.vue'
import {
  CertificateTemplateApi,
  type CertificateTemplate,
  type CertificateTemplatePageParams,
  certificateTypeMap,
  templateStatusMap
} from '@/api/infra/certificateTemplate/index'

// 统计卡片数据
const stats = ref([
  {
    label: '证书模板总数',
    value: 0,
    icon: 'Tickets',
    iconClass: 'stat-icon1',
    bgClass: 'stat-bg1'
  },
  {
    label: '启用中',
    value: 0,
    icon: 'Check',
    iconClass: 'stat-icon2',
    bgClass: 'stat-bg2'
  },
  {
    label: '已停用',
    value: 0,
    icon: 'Close',
    iconClass: 'stat-icon3',
    bgClass: 'stat-bg3'
  },
  {
    label: '草稿状态',
    value: 0,
    icon: 'EditPen',
    iconClass: 'stat-icon4',
    bgClass: 'stat-bg4'
  }
])

// 表格数据和加载状态
const tableData = ref<CertificateTemplate[]>([])
const loading = ref(false)
const statisticsLoading = ref(false)
const statusLoading = ref<Record<number, boolean>>({})

// 筛选条件
const filter = ref({ type: '', status: '', keyword: '' })

// 分页信息
const pagination = ref({ page: 1, size: 10, total: 0 })

// 获取证书模板列表数据
const fetchTemplateList = async () => {
  try {
    loading.value = true

    // 构建查询参数
    const params: CertificateTemplatePageParams = {
      page: pagination.value.page,
      size: pagination.value.size
    }

    // 添加筛选条件
    if (filter.value.type) {
      params.type = filter.value.type
    }
    if (filter.value.status) {
      params.status = filter.value.status
    }
    if (filter.value.keyword) {
      params.keyword = filter.value.keyword
    }

    // 调用API获取数据
    const result = await CertificateTemplateApi.getPage(params)

    tableData.value = result.list
    pagination.value.total = result.total
  } catch (error) {
    console.error('获取证书模板列表失败:', error)
    ElMessage.error('获取证书模板列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const fetchStatistics = async () => {
  try {
    statisticsLoading.value = true
    const result = await CertificateTemplateApi.getStatistics()

    stats.value[0].value = result.total
    stats.value[1].value = result.active_count
    stats.value[2].value = result.inactive_count
    stats.value[3].value = result.draft_count
  } catch (error) {
    console.error('获取统计数据失败:', error)
  } finally {
    statisticsLoading.value = false
  }
}

// 筛选方法
const onFilter = () => {
  pagination.value.page = 1 // 重置到第一页
  fetchTemplateList()
}

// 分页处理
const handleSizeChange = (size: number) => {
  pagination.value.size = size
  pagination.value.page = 1
  fetchTemplateList()
}

const handleCurrentChange = (page: number) => {
  pagination.value.page = page
  fetchTemplateList()
}

// 操作方法
function onEdit(row: CertificateTemplate) {
  currentEditRow.value = row
  editDrawerVisible.value = true
}

function onPreview(row: CertificateTemplate) {
  currentPreviewRow.value = row
  previewDrawerVisible.value = true
}

async function onEnable(row: CertificateTemplate) {
  if (!row.id) return

  try {
    statusLoading.value[row.id] = true
    await CertificateTemplateApi.updateStatus(row.id, 'active')
    ElMessage.success('模板已启用')
    // 刷新列表和统计数据
    fetchTemplateList()
    fetchStatistics()
  } catch (error) {
    console.error('启用模板失败:', error)
    ElMessage.error('启用失败，请重试')
  } finally {
    statusLoading.value[row.id] = false
  }
}

async function onDisable(row: CertificateTemplate) {
  if (!row.id) return

  try {
    statusLoading.value[row.id] = true
    await CertificateTemplateApi.updateStatus(row.id, 'inactive')
    ElMessage.success('模板已停用')
    // 刷新列表和统计数据
    fetchTemplateList()
    fetchStatistics()
  } catch (error) {
    console.error('停用模板失败:', error)
    ElMessage.error('停用失败，请重试')
  } finally {
    statusLoading.value[row.id] = false
  }
}

// 编辑成功回调
function handleEditSuccess() {
  // 刷新列表和统计数据
  fetchTemplateList()
  fetchStatistics()
}

// 编辑抽屉显示控制
const editDrawerVisible = ref(false)
const currentEditRow = ref<CertificateTemplate | null>(null)

// 预览抽屉显示控制
const previewDrawerVisible = ref(false)
const currentPreviewRow = ref<CertificateTemplate | null>(null)

// 页面初始化
onMounted(() => {
  fetchTemplateList()
  fetchStatistics()
})
</script>

<style scoped lang="scss">
.certificate-template-page {
  padding: 24px;
  background: #fff;
}
.stats-row {
  margin-bottom: 16px;
}
.stat-card {
  border: none;
  box-shadow: 0 2px 8px #f0f1f2;
  border-radius: 8px;
  padding: 0;
}
.stat-card-inner {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 0 8px 8px;
}
.stat-icon {
  font-size: 24px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
}
.stat-icon1 {
  color: #409eff;
  background: #e6f1ff;
}
.stat-icon2 {
  color: #67c23a;
  background: #e8f7e0;
}
.stat-icon3 {
  color: #e6a23c;
  background: #fff5e6;
}
.stat-icon4 {
  color: #f56c6c;
  background: #ffeaea;
}
.stat-bg1 {
  background: linear-gradient(90deg, #e6f1ff 0%, #cce3ff 100%);
}
.stat-bg2 {
  background: linear-gradient(90deg, #e8f7e0 0%, #d2f2c2 100%);
}
.stat-bg3 {
  background: linear-gradient(90deg, #fff5e6 0%, #ffe1b8 100%);
}
.stat-bg4 {
  background: linear-gradient(90deg, #ffeaea 0%, #ffd6d6 100%);
}
.stat-num {
  font-size: 18px;
  font-weight: bold;
}
.stat-label {
  color: #888;
  font-size: 12px;
}
.mb-4 {
  margin-bottom: 24px;
}
.filter-row {
  margin-bottom: 8px;
}
.template-table {
  margin-top: 8px;
}
@media (max-width: 768px) {
  .certificate-template-page {
    padding: 8px;
  }
  .stat-card {
    flex-direction: column;
    align-items: flex-start;
    .stat-icon {
      margin-bottom: 8px;
      margin-right: 0;
    }
  }
  .filter-row {
    flex-wrap: wrap;
    .el-col {
      margin-bottom: 8px;
    }
  }
}
</style>
