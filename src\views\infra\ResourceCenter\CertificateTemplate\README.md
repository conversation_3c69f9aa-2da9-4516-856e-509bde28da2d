# 证书模板管理功能

## 功能概述

证书模板管理功能提供了完整的证书模板CRUD操作，包括：

- 证书模板列表查看和筛选
- 新增和编辑证书模板
- 证书模板预览
- 模板状态管理（启用/停用）
- 统计数据展示

## 技术架构

### 1. 接口层 (`src/api/infra/certificateTemplate/index.ts`)

提供了完整的API封装，包括：

- `getPage()` - 分页查询证书模板
- `getDetail()` - 获取证书模板详情
- `create()` - 创建新证书模板
- `update()` - 更新证书模板
- `delete()` - 删除证书模板
- `updateStatus()` - 更新模板状态
- `getStatistics()` - 获取统计数据
- `getList()` - 获取模板列表（不分页）

### 2. 状态管理 (`src/store/modules/certificateTemplate.ts`)

使用Pinia进行状态管理，提供：

- 模板列表管理
- 当前编辑模板状态
- 加载状态管理
- 统一的错误处理

### 3. 页面组件

#### 主页面 (`CertificateTemplate.vue`)
- 统计卡片展示
- 筛选和搜索功能
- 分页表格展示
- 操作按钮（编辑、预览、启用/停用）

#### 添加/编辑组件 (`AddCertificateTemplate.vue`)
- 拖拽式字段设计器
- 实时预览功能
- 字段样式配置
- 背景图片上传

#### 预览组件 (`PreviewCertificate.vue`)
- 证书模板预览
- 支持HTML模板渲染
- 字段数据模拟显示

## 数据结构

### 证书模板字段 (`CertificateTemplateField`)

```typescript
interface CertificateTemplateField {
  id: string                // 字段ID
  field_type: string        // 字段类型：name/code/id/date/qrcode
  field_label: string       // 字段标签
  position_x: number        // X坐标位置
  position_y: number        // Y坐标位置
  font_size: number         // 字体大小
  font_color: string        // 字体颜色
  font_family: string       // 字体族
  sort_order: number        // 排序顺序
}
```

### 证书模板 (`CertificateTemplate`)

```typescript
interface CertificateTemplate {
  id?: number               // 模板ID
  name: string              // 模板名称
  type: string              // 证书类型：training/completion/skill
  description?: string      // 模板描述
  background?: string       // 背景图片文件名
  background_url?: string   // 背景图片URL
  course?: string           // 适用课程ID
  course_name?: string      // 适用课程名称
  status: string            // 模板状态：draft/active/inactive
  html_content?: string     // HTML模板内容
  preview_url?: string      // 预览图URL
  fields?: CertificateTemplateField[]  // 字段配置
  creator?: string          // 创建人
  creator_name?: string     // 创建人姓名
  create_time?: string      // 创建时间
  updater?: string          // 更新人
  update_time?: string      // 更新时间
}
```

## 使用说明

### 1. 查看证书模板列表

访问证书模板管理页面，可以：
- 查看所有证书模板的统计信息
- 使用筛选条件（类型、状态、关键词）过滤模板
- 分页浏览模板列表
- 查看每个模板的基本信息和状态

### 2. 创建新证书模板

1. 点击"新增"按钮打开编辑器
2. 填写基础信息（名称、类型、描述等）
3. 上传背景图片（可选）
4. 从左侧拖拽字段到证书预览区域
5. 选择字段并在右侧配置样式（字体、颜色、位置等）
6. 保存模板

### 3. 编辑现有模板

1. 在列表中点击"编辑"按钮
2. 修改基础信息或字段配置
3. 保存更改

### 4. 预览证书模板

1. 在列表中点击"预览"按钮
2. 查看证书的实际效果
3. 支持HTML模板和字段渲染两种预览方式

### 5. 管理模板状态

- 点击"启用"按钮将模板设为启用状态
- 点击"停用"按钮将模板设为停用状态
- 只有启用状态的模板才能用于生成证书

## 开发说明

### 1. 添加新的字段类型

1. 在 `fieldTypeMap` 中添加新的字段类型映射
2. 在 `AddCertificateTemplate.vue` 的 `fieldTypes` 数组中添加新字段
3. 在 `PreviewCertificate.vue` 中添加对应的显示逻辑

### 2. 扩展证书类型

1. 在 `certificateTypeMap` 中添加新的证书类型
2. 在 `certificateTypeOptions` 中添加选项
3. 更新后端接口文档中的类型定义

### 3. 自定义样式配置

可以在 `AddCertificateTemplate.vue` 的样式配置区域添加更多字段属性，如：
- 字体粗细
- 文字对齐方式
- 边框样式
- 阴影效果

### 4. 测试

使用提供的测试文件 `test.ts` 来验证API接口：

```javascript
// 在浏览器控制台中运行
testCertificateTemplateApi()  // 测试所有API接口
testDataMappings()           // 测试数据映射
validateTypeDefinitions()   // 验证类型定义
```

## 注意事项

1. **接口调用**：所有API调用都已封装在 `CertificateTemplateApi` 中，请勿直接在组件中调用接口
2. **类型安全**：项目使用TypeScript，请确保所有数据结构符合类型定义
3. **错误处理**：所有API调用都包含错误处理，会自动显示错误消息
4. **状态管理**：使用Pinia进行状态管理，组件间数据共享通过store实现
5. **响应式设计**：所有组件都支持响应式布局，适配不同屏幕尺寸

## 后续优化建议

1. 添加模板导入/导出功能
2. 支持更多字段类型（图片、签名等）
3. 增加模板版本管理
4. 添加批量操作功能
5. 优化拖拽体验和视觉反馈
