/**
 * 证书模板API测试文件
 * 用于验证接口对接的正确性
 */

import { 
  CertificateTemplateApi,
  type CertificateTemplatePageParams,
  type CreateCertificateTemplateParams,
  type UpdateCertificateTemplateParams,
  certificateTypeMap,
  templateStatusMap,
  fieldTypeMap
} from './index'

// 测试分页查询参数
const testPageParams: CertificateTemplatePageParams = {
  page: 1,
  size: 10,
  type: 'training',
  status: 'active',
  keyword: '测试'
}

// 测试创建模板参数
const testCreateParams: CreateCertificateTemplateParams = {
  name: '测试证书模板',
  type: 'training',
  description: '这是一个测试证书模板',
  background: 'test-bg.jpg',
  background_url: 'https://example.com/test-bg.jpg',
  course: 'TEST_COURSE',
  course_name: '测试课程',
  status: 'draft',
  fields: [
    {
      id: 'field_1',
      field_type: 'name',
      field_label: '【学员姓名】',
      position_x: 350,
      position_y: 200,
      font_size: 32,
      font_color: '#333',
      font_family: '微软雅黑',
      sort_order: 1
    },
    {
      id: 'field_2',
      field_type: 'date',
      field_label: '【发证日期】',
      position_x: 350,
      position_y: 400,
      font_size: 16,
      font_color: '#666',
      font_family: '微软雅黑',
      sort_order: 2
    }
  ]
}

// 测试更新模板参数
const testUpdateParams: UpdateCertificateTemplateParams = {
  ...testCreateParams,
  id: 1,
  name: '更新后的测试证书模板'
}

/**
 * 测试所有API接口
 */
export async function testCertificateTemplateApi() {
  console.log('开始测试证书模板API...')
  
  try {
    // 1. 测试分页查询
    console.log('1. 测试分页查询接口...')
    const pageResult = await CertificateTemplateApi.getPage(testPageParams)
    console.log('分页查询结果:', pageResult)
    
    // 2. 测试统计接口
    console.log('2. 测试统计接口...')
    const statistics = await CertificateTemplateApi.getStatistics()
    console.log('统计结果:', statistics)
    
    // 3. 测试列表查询
    console.log('3. 测试列表查询接口...')
    const listResult = await CertificateTemplateApi.getList('active')
    console.log('列表查询结果:', listResult)
    
    // 4. 测试创建模板
    console.log('4. 测试创建模板接口...')
    const createResult = await CertificateTemplateApi.create(testCreateParams)
    console.log('创建结果:', createResult)
    
    // 5. 测试获取详情
    if (createResult.id) {
      console.log('5. 测试获取详情接口...')
      const detailResult = await CertificateTemplateApi.getDetail(createResult.id)
      console.log('详情结果:', detailResult)
      
      // 6. 测试更新模板
      console.log('6. 测试更新模板接口...')
      const updateData = { ...testUpdateParams, id: createResult.id }
      await CertificateTemplateApi.update(updateData)
      console.log('更新成功')
      
      // 7. 测试状态更新
      console.log('7. 测试状态更新接口...')
      await CertificateTemplateApi.updateStatus(createResult.id, 'active')
      console.log('状态更新成功')
      
      // 8. 测试删除模板
      console.log('8. 测试删除模板接口...')
      await CertificateTemplateApi.delete(createResult.id)
      console.log('删除成功')
    }
    
    console.log('所有API测试完成！')
    
  } catch (error) {
    console.error('API测试失败:', error)
  }
}

/**
 * 测试数据映射
 */
export function testDataMappings() {
  console.log('测试数据映射...')
  
  console.log('证书类型映射:', certificateTypeMap)
  console.log('模板状态映射:', templateStatusMap)
  console.log('字段类型映射:', fieldTypeMap)
  
  // 测试映射功能
  const testType = 'training'
  const testStatus = 'active'
  const testFieldType = 'name'
  
  console.log(`类型 ${testType} 映射为: ${certificateTypeMap[testType]}`)
  console.log(`状态 ${testStatus} 映射为: ${templateStatusMap[testStatus]}`)
  console.log(`字段类型 ${testFieldType} 映射为: ${fieldTypeMap[testFieldType]}`)
}

/**
 * 验证类型定义
 */
export function validateTypeDefinitions() {
  console.log('验证类型定义...')
  
  // 验证参数类型
  const pageParams: CertificateTemplatePageParams = testPageParams
  const createParams: CreateCertificateTemplateParams = testCreateParams
  const updateParams: UpdateCertificateTemplateParams = testUpdateParams
  
  console.log('类型验证通过:', {
    pageParams: typeof pageParams,
    createParams: typeof createParams,
    updateParams: typeof updateParams
  })
}

// 导出测试函数，可在控制台中调用
if (typeof window !== 'undefined') {
  (window as any).testCertificateTemplateApi = testCertificateTemplateApi
  (window as any).testDataMappings = testDataMappings
  (window as any).validateTypeDefinitions = validateTypeDefinitions
}
