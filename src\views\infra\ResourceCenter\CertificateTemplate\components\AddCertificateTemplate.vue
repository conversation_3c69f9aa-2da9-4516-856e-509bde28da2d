<!--
  页面名称：新建证书模板
  功能描述：支持动态字段拖拽、证书预览、字段样式配置、基础信息填写与保存、字段唯一性、字段删除、证书背景上传、字段定位、内容自定义、字段锁定、背景裁剪
-->
<template>
  <el-drawer
    v-model="drawerVisible"
    title="新建证书模板"
    direction="rtl"
    size="80%"
    :before-close="handleClose"
    class="add-certificate-template-drawer"
  >
    <div class="main-layout">
      <!-- 左侧：动态字段组件 -->
      <div class="left-panel">
        <div class="panel-title">动态字段组件</div>
        <el-button
          v-for="field in fieldTypes"
          :key="field.type"
          class="field-btn"
          plain
          draggable="true"
          @dragstart="(e) => handleDragStart(e, field)"
        >
          <el-icon><component :is="field.icon" /></el-icon>{{ field.label }}
        </el-button>
      </div>
      <!-- 中间：证书预览 -->
      <div class="center-panel">
        <div class="panel-title">证书预览</div>
        <div class="certificate-preview-area">
          <el-upload
            class="upload-bg-btn"
            action="#"
            :show-file-list="false"
            :auto-upload="false"
            :on-change="handleBgImageChange"
            :disabled="bgImageUploading"
            accept="image/*"
          >
            <el-button
              size="small"
              type="primary"
              style="margin-bottom: 12px"
              :loading="bgImageUploading"
              :disabled="bgImageUploading"
            >
              {{ bgImageUploading ? '上传中...' : '上传证书背景图' }}
            </el-button>
          </el-upload>
          <div
            class="preview-certificate-box"
            :style="backgroundImageStyle"
            @dragover.prevent
            @drop="handleDrop"
            ref="previewBoxRef"
          >
            <!-- 渲染已添加的字段 -->
            <div
              v-for="field in fields"
              :key="field.id"
              class="draggable-field"
              :style="{
                left: field.position_x + 'px',
                top: field.position_y + 'px',
                position: 'absolute',
                fontSize: field.font_size + 'px',
                color: field.font_color,
                fontFamily: field.font_family,
                cursor: field.selected ? 'move' : 'pointer',
                border: field.selected ? '1px solid #409EFF' : '1px dashed #bbb',
                background: field.selected ? '#e6f7ff' : 'rgba(255,255,255,0.7)',
                padding: '2px 8px',
                borderRadius: '4px',
                zIndex: field.selected ? 2 : 1
              }"
              @click.stop="selectField(field.id)"
              @mousedown="(e) => handleFieldMouseDown(e, field)"
            >
              {{ field.field_label }}
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧：样式配置 -->
      <div class="right-panel">
        <div class="panel-title">样式配置</div>
        <template v-if="selectedField">
          <el-form label-width="60px" class="style-form">
            <el-form-item label="字体">
              <el-select v-model="selectedField.font_family" style="width: 100%">
                <el-option
                  v-for="option in fontFamilyOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="字号">
              <el-input v-model.number="selectedField.font_size" type="number" min="10" max="80" />
            </el-form-item>
            <el-form-item label="颜色">
              <el-color-picker v-model="selectedField.font_color" />
            </el-form-item>
            <el-form-item label="位置X">
              <el-input v-model.number="selectedField.position_x" type="number" min="0" max="900" />
            </el-form-item>
            <el-form-item label="位置Y">
              <el-input v-model.number="selectedField.position_y" type="number" min="0" max="600" />
            </el-form-item>
          </el-form>
        </template>
        <template v-else>
          <div class="style-config-tip">点击证书上的字段进行样式配置</div>
        </template>
      </div>
    </div>
    <!-- 下方：基础信息表单 -->
    <div class="base-info-section">
      <div class="base-info-title">基础信息</div>
      <el-form :model="formData" label-width="100px" class="base-info-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="模板名称">
              <el-input v-model="formData.name" placeholder="例如：新媒体初级培训证书模板" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="证书类型">
              <el-select v-model="formData.type" placeholder="请选择证书类型">
                <el-option
                  v-for="option in certificateTypeOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="适用课程">
              <el-input v-model="formData.course" placeholder="选择适用的课程" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-select v-model="formData.status" placeholder="请选择状态">
                <el-option
                  v-for="option in templateStatusOptions"
                  :key="option.value"
                  :label="option.label"
                  :value="option.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <!-- 底部按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存模板</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, watch, reactive, computed, onBeforeUnmount, h } from 'vue'
import { ElMessage } from 'element-plus'
import { User, CreditCard, Tickets, Calendar } from '@element-plus/icons-vue'
import { Icon } from '@/components/Icon'
import request from '@/config/axios'
import {
  CertificateTemplateApi,
  type CertificateTemplateField,
  type CreateCertificateTemplateParams,
  type UpdateCertificateTemplateParams,
  certificateTypeOptions,
  templateStatusOptions,
  fontFamilyOptions
} from '@/api/infra/certificateTemplate/index'

interface Props {
  visible: boolean
  editData?: any // 编辑时传入的数据
}
const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void // 保存成功事件
}>()

// 拖拽字段类型定义
interface FieldType {
  type: string
  label: string
  icon: any
}

// 二维码图标组件
const QrCodeIcon = () => h(Icon, { icon: 'tdesign:qrcode' })

const fieldTypes: FieldType[] = [
  { type: 'name', label: '【学员姓名】', icon: User },
  { type: 'code', label: '【证书编号】', icon: CreditCard },
  { type: 'id', label: '【身份证】', icon: Tickets },
  { type: 'date', label: '【发证日期】', icon: Calendar },
  { type: 'qrcode', label: '【证书查验二维码】', icon: QrCodeIcon }
]

// 拖拽数据
let dragFieldType: FieldType | null = null
const handleDragStart = (e: DragEvent, field: any) => {
  dragFieldType = field
}

const previewBoxRef = ref<HTMLElement | null>(null)

// 扩展字段类型，添加前端使用的属性
interface ExtendedCertificateField extends CertificateTemplateField {
  selected?: boolean
}

// 证书字段数据
let idSeed = 1
const fields = ref<ExtendedCertificateField[]>([])

const handleDrop = (e: DragEvent) => {
  if (!dragFieldType) return
  const box = previewBoxRef.value
  if (!box) return
  // 计算相对坐标
  const rect = box.getBoundingClientRect()
  const x = e.clientX - rect.left
  const y = e.clientY - rect.top

  const newField: ExtendedCertificateField = {
    id: 'field_' + idSeed++,
    field_type: dragFieldType.type,
    field_label: dragFieldType.label,
    position_x: x - 40, // 居中微调
    position_y: y - 15,
    font_size: 20,
    font_color: '#333',
    font_family: '微软雅黑',
    sort_order: fields.value.length + 1,
    selected: false
  }

  fields.value.push(newField)
  dragFieldType = null
}

const selectField = (id: string) => {
  fields.value.forEach((f) => {
    if ('selected' in f) {
      f.selected = f.id === id
    }
  })
}

// 拖动字段相关
let draggingField: ExtendedCertificateField | null = null
let dragStart = { x: 0, y: 0 }
let fieldStart = { x: 0, y: 0 }

const handleFieldMouseDown = (e: MouseEvent, field: ExtendedCertificateField) => {
  if (!field.selected) return
  draggingField = field
  dragStart = { x: e.clientX, y: e.clientY }
  fieldStart = { x: field.position_x, y: field.position_y }
  document.addEventListener('mousemove', handleFieldMouseMove)
  document.addEventListener('mouseup', handleFieldMouseUp)
}
const handleFieldMouseMove = (e: MouseEvent) => {
  if (!draggingField) return
  const dx = e.clientX - dragStart.x
  const dy = e.clientY - dragStart.y
  draggingField.position_x = Math.max(0, Math.min(fieldStart.x + dx, 900 - 80)) // 限制在容器内
  draggingField.position_y = Math.max(0, Math.min(fieldStart.y + dy, 600 - 30))
}
const handleFieldMouseUp = () => {
  draggingField = null
  document.removeEventListener('mousemove', handleFieldMouseMove)
  document.removeEventListener('mouseup', handleFieldMouseUp)
}
onBeforeUnmount(() => {
  document.removeEventListener('mousemove', handleFieldMouseMove)
  document.removeEventListener('mouseup', handleFieldMouseUp)
})

// 本地可写变量
const drawerVisible = ref(props.visible)
watch(
  () => props.visible,
  (val) => {
    drawerVisible.value = val
    // 当抽屉打开时，初始化编辑数据
    if (val && props.editData) {
      initEditData()
    } else if (val) {
      // 新增模式，重置表单
      resetForm()
    }
  }
)
watch(drawerVisible, (val) => {
  if (!val) emit('update:visible', false)
})

const formData = reactive({
  name: '',
  type: '',
  description: '',
  background: '',
  course: '',
  status: 'draft'
})

// 新增：证书背景图片
const bgImage = ref<string | null>(null)
const bgImageUploading = ref(false) // 上传状态

const handleBgImageChange = (file: any) => {
  if (file && file.raw) {
    bgImageUploading.value = true
    const formData = new FormData()
    formData.append('file', file.raw)
    formData.append('directory', 'certificate')

    request
      .postOriginal({
        url: '/infra/file/upload',
        data: formData,
        headers: { 'Content-Type': 'multipart/form-data' }
      })
      .then((res) => {
        if (res.code === 0 && res.data && /^https?:\/\//.test(res.data)) {
          bgImage.value = res.data
          ElMessage.success('背景图片上传成功')
        } else {
          bgImage.value = null
          ElMessage.error(res.msg || '背景图片上传失败')
        }
      })
      .catch(() => {
        bgImage.value = null
        ElMessage.error('背景图片上传失败')
      })
      .finally(() => {
        bgImageUploading.value = false
      })
  }
}

const backgroundImageStyle = computed(() => {
  return bgImage.value
    ? {
        backgroundImage: `url(${bgImage.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }
    : {}
})

const handleClose = () => {
  drawerVisible.value = false
}

const handleSubmit = async () => {
  // 表单验证
  if (!formData.name) {
    ElMessage.warning('请输入模板名称')
    return
  }
  if (!formData.type) {
    ElMessage.warning('请选择模板类型')
    return
  }

  try {
    // 准备字段数据，移除前端专用属性
    const fieldsData: CertificateTemplateField[] = fields.value.map((field) => ({
      id: field.id,
      field_type: field.field_type,
      field_label: field.field_label,
      position_x: field.position_x,
      position_y: field.position_y,
      font_size: field.font_size,
      font_color: field.font_color,
      font_family: field.font_family,
      sort_order: field.sort_order
    }))

    // 准备保存数据
    const templateData: CreateCertificateTemplateParams = {
      name: formData.name,
      type: formData.type,
      description: formData.description,
      background: formData.background,
      background_url: bgImage.value || '', // 使用OSS上传后的URL
      course: formData.course,
      course_name: formData.course, // 暂时使用相同值
      status: formData.status,
      fields: fieldsData
    }

    // 判断是新增还是编辑
    if (props.editData?.id) {
      // 编辑模式
      const updateData: UpdateCertificateTemplateParams = {
        ...templateData,
        id: props.editData.id
      }
      await CertificateTemplateApi.update(updateData)
      ElMessage.success('证书模板更新成功')
      emit('success')
      handleClose()
    } else {
      // 新增模式
      const result = await CertificateTemplateApi.create(templateData)
      if (result.id) {
        ElMessage.success('证书模板创建成功')
        emit('success')
        handleClose()
      }
    }
  } catch (error) {
    console.error('保存证书模板失败:', error)
    ElMessage.error('保存失败，请重试')
  }
}

const handleBackgroundChange = (file: any) => {
  formData.background = file.name
}

const selectedField = computed(() => fields.value.find((f) => f.selected))

// 初始化编辑数据
const initEditData = () => {
  if (props.editData) {
    Object.assign(formData, {
      name: props.editData.name || '',
      type: props.editData.type || '',
      description: props.editData.description || '',
      background: props.editData.background || '',
      course: props.editData.course || '',
      status: props.editData.status || 'draft'
    })

    // 初始化字段数据
    if (props.editData.fields && Array.isArray(props.editData.fields)) {
      fields.value = props.editData.fields.map((field) => ({
        ...field,
        selected: false
      }))
      idSeed = Math.max(...fields.value.map((f) => parseInt(f.id.replace('field_', '')) || 0)) + 1
    }

    // 初始化背景图片
    if (props.editData.background_url) {
      bgImage.value = props.editData.background_url
    }
  }
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    name: '',
    type: '',
    description: '',
    background: '',
    course: '',
    status: 'draft'
  })
  fields.value = []
  bgImage.value = null
  idSeed = 1
}
</script>

<style scoped lang="scss">
.add-certificate-template-drawer {
  .el-drawer__body {
    background: #fafbfc;
    padding: 0 0 24px 0;
  }
}
.main-layout {
  display: flex;
  flex-direction: row;
  gap: 24px;
  min-height: 400px;
}
.left-panel {
  width: 220px;
  background: #fff;
  border-radius: 8px;
  padding: 24px 12px 12px 12px;
  box-shadow: 0 1px 4px #f0f1f2;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.panel-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 18px;
}
.field-btn {
  width: 100%;
  margin: 0 0 12px 0 !important;
  justify-content: flex-start;
  font-size: 15px;
  cursor: grab;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.field-btn .el-icon {
  font-size: 16px;
  color: #409eff;
  margin-right: 0 !important;
  flex-shrink: 0;
}

.field-btn:hover {
  border-color: #409eff;
  background-color: #ecf5ff;
}
.center-panel {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 24px 0 0 0;
  box-shadow: 0 1px 4px #f0f1f2;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -90px;
}
.certificate-preview-area {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.upload-bg-btn {
  margin-bottom: 8px;
}
.preview-certificate-box {
  width: 900px;
  height: 600px;
  border: 2px dashed #dcdfe6;
  border-radius: 12px;
  background: #fcfcfd;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 48px 0 48px 0;
  margin-top: 8px;
  position: relative;
  background-repeat: no-repeat;
  overflow: hidden;
}
.draggable-field {
  user-select: none;
  transition: box-shadow 0.2s;
  /* 禁止文本选中 */
}
.preview-field {
  border-style: dashed;
  font-size: 20px;
  margin-bottom: 0;
}
.preview-main-text {
  color: #888;
  font-size: 32px;
  margin: 24px 0;
}
.right-panel {
  width: 220px;
  background: #fff;
  border-radius: 8px;
  padding: 24px 12px 12px 12px;
  box-shadow: 0 1px 4px #f0f1f2;
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.style-config-tip {
  color: #888;
  font-size: 15px;
  margin-top: 32px;
  text-align: center;
}
.base-info-section {
  background: #fff;
  border-radius: 8px;
  margin: 32px 0 0 0;
  padding: 24px 32px 8px 32px;
  box-shadow: 0 1px 4px #f0f1f2;
}
.base-info-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 18px;
}
.base-info-form {
  margin-top: 0;
}
.drawer-footer {
  text-align: right;
  padding: 20px 32px 0 0;
  border-top: 1px solid #e4e7ed;
  background: #fff;
}
.style-form {
  margin-top: 16px;
  .el-form-item {
    margin-bottom: 16px;
  }
}
@media (max-width: 1200px) {
  .main-layout {
    flex-direction: column;
    gap: 12px;
  }
  .left-panel,
  .right-panel,
  .center-panel {
    width: 100% !important;
    min-width: 0;
    margin-bottom: 12px;
  }

  .field-btn {
    margin: 0 0 8px 0 !important;
    padding: 6px 10px;
    font-size: 14px;
  }

  .field-btn .el-icon {
    font-size: 14px;
  }
  .certificate-preview-area {
    justify-content: flex-start;
  }
  .preview-certificate-box {
    width: 100%;
    min-width: 320px;
    max-width: calc(100vw - 48px);
    height: auto;
    aspect-ratio: 3/2;
    min-height: 200px;
    max-height: 60vw;
    overflow-x: auto;
    overflow-y: auto;
  }
}
@media (max-width: 900px) {
  .main-layout {
    padding: 0 4px;
  }
  .preview-certificate-box {
    padding: 16px 0 16px 0;
    font-size: 14px;
  }
  .base-info-section {
    padding: 12px 4px 8px 4px;
  }
}
// 让el-form、el-input、el-select等控件宽度100%
.base-info-form .el-form-item__content,
.base-info-form .el-input,
.base-info-form .el-select {
  width: 100% !important;
}
// 让抽屉body可滚动
.add-certificate-template-drawer .el-drawer__body {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 100vh;
}
</style>
