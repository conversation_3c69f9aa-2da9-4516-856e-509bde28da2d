import request from '@/config/axios'

// 证书模板字段类型
export interface CertificateField {
  id: string
  field_type: string // 对应接口文档中的field_type
  field_label: string // 对应接口文档中的field_label
  position_x: number // 对应接口文档中的position_x
  position_y: number // 对应接口文档中的position_y
  font_size: number // 对应接口文档中的font_size
  font_color: string // 对应接口文档中的font_color
  font_family: string // 对应接口文档中的font_family
  sort_order: number // 对应接口文档中的sort_order
  // 前端使用的字段（不传给后端）
  selected?: boolean
}

// 证书模板数据结构
export interface CertificateTemplateVO {
  id?: number
  name: string
  type: string
  description?: string
  background?: string
  background_url?: string // 对应接口文档中的background_url
  course?: string
  course_name?: string // 对应接口文档中的course_name
  status: string
  html_content?: string // 对应接口文档中的html_content
  preview_url?: string // 对应接口文档中的preview_url
  fields?: CertificateField[]
  creator?: string
  creator_name?: string // 对应接口文档中的creator_name
  create_time?: string // 对应接口文档中的create_time
  updater?: string
  update_time?: string // 对应接口文档中的update_time
}

// 分页查询参数
export interface CertificateTemplatePageParams {
  page?: number
  size?: number
  type?: string
  status?: string
  keyword?: string
}

// 分页查询结果
export interface CertificateTemplatePageResult {
  total: number
  list: CertificateTemplateVO[]
}

// 新增证书模板参数
export interface CreateCertificateTemplateParams {
  name: string
  type: string
  description?: string
  background?: string
  background_url?: string
  course?: string
  course_name?: string
  status: string
  html_content?: string
  fields?: CertificateField[]
}

// 更新证书模板参数
export interface UpdateCertificateTemplateParams extends CreateCertificateTemplateParams {
  id: number
}

// 证书模板统计结果
export interface CertificateTemplateStatisticsResult {
  total: number
  active_count: number
  inactive_count: number
  draft_count: number
}

// 证书模板列表项
export interface CertificateTemplateListItem {
  id: number
  name: string
  type: string
  status: string
}

// 证书模板API
export const CertificateTemplateApi = {
  // 分页查询证书模板
  getPage: async (
    params: CertificateTemplatePageParams
  ): Promise<CertificateTemplatePageResult> => {
    return await request.get({ url: '/publicbiz/certificate/template/page', params })
  },

  // 获取证书模板详情
  getDetail: async (id: number): Promise<CertificateTemplateVO> => {
    return await request.get({ url: '/publicbiz/certificate/template/detail', params: { id } })
  },

  // 新增证书模板
  create: async (data: CreateCertificateTemplateParams): Promise<{ id: number }> => {
    return await request.post({ url: '/publicbiz/certificate/template/create', data })
  },

  // 更新证书模板
  update: async (data: UpdateCertificateTemplateParams): Promise<void> => {
    return await request.post({ url: '/publicbiz/certificate/template/update', data })
  },

  // 删除证书模板
  delete: async (id: number): Promise<void> => {
    return await request.post({ url: '/publicbiz/certificate/template/delete', data: { id } })
  },

  // 更新证书模板状态
  updateStatus: async (id: number, status: string): Promise<void> => {
    return await request.post({
      url: '/publicbiz/certificate/template/updateStatus',
      data: { id, status }
    })
  },

  // 获取证书模板统计报表
  getStatistics: async (): Promise<CertificateTemplateStatisticsResult> => {
    return await request.get({ url: '/publicbiz/certificate/template/statistics' })
  },

  // 获取证书模板列表（不分页）
  getList: async (status?: string): Promise<CertificateTemplateListItem[]> => {
    const params = status ? { status } : {}
    return await request.get({ url: '/publicbiz/certificate/template/list', params })
  }
}

// Mock数据 - 用于开发测试
export const mockCertificateTemplates: CertificateTemplateVO[] = [
  {
    id: 1,
    name: '新媒体初级培训证书模板',
    type: 'training',
    description: '新媒体初级培训证书模板描述',
    background: 'certificate-bg-001.jpg',
    background_url: 'https://example.com/images/backgrounds/certificate-bg-001.jpg',
    course: 'COURSE_001',
    course_name: '新媒体初级培训',
    status: 'active',
    fields: [
      {
        id: 'field_1',
        field_type: 'name',
        field_label: '【学员姓名】',
        position_x: 350,
        position_y: 200,
        font_size: 32,
        font_color: '#333',
        font_family: '微软雅黑',
        sort_order: 1
      },
      {
        id: 'field_2',
        field_type: 'date',
        field_label: '【发证日期】',
        position_x: 350,
        position_y: 400,
        font_size: 16,
        font_color: '#666',
        font_family: '微软雅黑',
        sort_order: 2
      }
    ],
    html_content: `<div class="certificate-template" style="width: 900px; height: 600px; position: relative; background: #fcfcfd; border: 2px dashed #dcdfe6; border-radius: 12px; padding: 48px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="font-size: 32px; font-weight: bold; color: #333; margin-bottom: 20px;">{{studentName}}</div>
        </div>
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="font-size: 22px; font-weight: bold; color: #333;">新媒体初级培训证书</div>
        </div>
        <div style="text-align: center; margin-bottom: 40px;">
          <div style="font-size: 16px; color: #666;">兹证明学员已完成相关培训课程</div>
        </div>
        <div style="text-align: center;">
          <div style="font-size: 16px; color: #222; font-weight: bold;">
            发证日期：{{issueDate}}
          </div>
        </div>
        <div style="position: absolute; bottom: 20px; right: 20px; font-size: 14px; color: #999;">
          证书编号：{{certificateCode}}
        </div>
      </div>`,
    preview_url: 'https://example.com/preview/cert-001.jpg',
    creator: 'admin',
    creator_name: '管理员',
    create_time: '2024-08-01 10:00:00',
    updater: 'admin',
    update_time: '2024-08-01 10:00:00'
  }
]

// 占位符映射配置
export const placeholderMap: Record<string, string> = {
  name: 'studentName',
  code: 'certificateCode',
  id: 'studentId',
  date: 'issueDate',
  qrcode: 'qrcodeUrl'
}

// 默认占位符数据
export const defaultPlaceholderData: Record<string, string> = {
  studentName: '张三',
  certificateCode: 'CERT-2024-001',
  studentId: '123456789012345678',
  issueDate: '2024年8月1日',
  qrcodeUrl: 'https://example.com/qrcode/cert-2024-001.png'
}

// 字段类型映射（前端显示用）
export const fieldTypeMap: Record<string, string> = {
  name: '学员姓名',
  code: '证书编号',
  id: '身份证号',
  date: '发证日期',
  qrcode: '证书查验二维码'
}

// 证书类型映射
export const certificateTypeMap: Record<string, string> = {
  training: '培训证书',
  completion: '结业证书',
  skill: '技能证书'
}

// 模板状态映射
export const templateStatusMap: Record<string, string> = {
  draft: '草稿',
  active: '启用中',
  inactive: '已停用'
}

// 字体族选项
export const fontFamilyOptions = [
  { label: '微软雅黑', value: '微软雅黑' },
  { label: '黑体', value: '黑体' },
  { label: 'Arial', value: 'Arial' },
  { label: 'Times New Roman', value: 'Times New Roman' },
  { label: '宋体', value: '宋体' }
]
