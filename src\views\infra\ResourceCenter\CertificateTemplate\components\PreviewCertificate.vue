<template>
  <el-dialog
    v-model="visible"
    title="证书预览"
    width="1000px"
    top="40px"
    :close-on-click-modal="false"
    :show-close="true"
    class="preview-certificate-dialog"
  >
    <div class="preview-certificate-main">
      <!-- 使用HTML模板渲染 -->
      <div v-if="renderedHtml" class="certificate-html-content" v-html="renderedHtml"></div>

      <!-- 降级方案：如果没有HTML模板，使用原来的字段渲染方式 -->
      <div v-else class="certificate-card" :style="backgroundImageStyle">
        <div class="card-content">
          <!-- 动态渲染字段 -->
          <div
            v-for="field in previewData.fields"
            :key="field.id"
            class="preview-field"
            :style="{
              position: 'absolute',
              left: field.position_x + 'px',
              top: field.position_y + 'px',
              fontSize: field.font_size + 'px',
              color: field.font_color,
              fontFamily: field.font_family
            }"
          >
            <!-- 二维码字段特殊处理 -->
            <div v-if="field.field_type === 'qrcode'" class="qrcode-placeholder">
              <div class="qrcode-box">
                <Icon icon="tdesign:qrcode" :size="Math.max(field.font_size * 2, 40)" />
              </div>
              <div
                class="qrcode-text"
                :style="{ fontSize: Math.max(field.font_size * 0.6, 12) + 'px' }"
              >
                证书查验二维码
              </div>
            </div>
            <!-- 普通字段 -->
            <span v-else>{{ getFieldDisplayValue(field) }}</span>
          </div>

          <!-- 如果没有字段，显示默认内容 -->
          <template v-if="!previewData.fields || previewData.fields.length === 0">
            <div class="student-name">张三</div>
            <div class="certificate-title">培训证书</div>
            <div class="certificate-desc">兹证明学员已完成相关培训课程</div>
            <div class="issue-date"> <span>发证日期：</span><b>2024年8月1日</b> </div>
          </template>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button @click="handleClose">关闭</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type {
  CertificateTemplateField,
  CertificateTemplate
} from '@/api/infra/certificateTemplate/index'
import { replacePlaceholders } from '@/utils/htmlSanitizer'
import { defaultPlaceholderData } from '@/api/infra/certificateTemplate/index'
import { Icon } from '@/components/Icon'

const props = defineProps<{ visible: boolean; data?: any }>()
const emit = defineEmits(['update:visible'])

const visible = ref(props.visible)
watch(
  () => props.visible,
  (v) => (visible.value = v)
)
watch(visible, (v) => emit('update:visible', v))

// 预览数据 - 使用传入的data
const previewData = computed((): CertificateTemplate => {
  // 使用传入的data
  if (props.data) {
    return props.data
  }

  // 默认数据
  return {
    name: '培训证书',
    type: 'training',
    description: '',
    background: '',
    course: '',
    status: 'draft',
    fields: []
  }
})

// 背景图片样式
const backgroundImageStyle = computed(() => {
  const bgUrl = previewData.value.background_url
  return bgUrl
    ? {
        backgroundImage: `url(${bgUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    : {}
})

// 渲染后的HTML内容
const renderedHtml = computed(() => {
  const template = previewData.value
  if (!template.html_content) {
    return null
  }

  // 准备占位符数据（移除模板名称）
  const placeholderData = {
    ...defaultPlaceholderData
    // 不再包含 templateName
  }

  // 安全地替换占位符并返回HTML
  return replacePlaceholders(template.html_content, placeholderData)
})

// 获取字段显示值
const getFieldDisplayValue = (field: CertificateTemplateField): string => {
  const mockData: Record<string, string> = {
    name: '张三',
    code: 'CERT-2024-001',
    id: '123456789012345678',
    date: '2024年8月1日',
    qrcode: '[二维码图片]'
  }

  return mockData[field.field_type] || field.field_label
}

const handleClose = () => {
  visible.value = false
}
</script>

<style scoped lang="scss">
.preview-certificate-dialog {
  .el-dialog__body {
    padding: 0 32px 24px 32px;
    background: #f8f9fa;
  }
}
.preview-certificate-main {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 480px;
}
.certificate-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 12px #e6e7e9;
  padding: 32px 32px 32px 32px;
  width: 520px;
  min-height: 380px;
  margin: 0 auto;
  position: relative;
}
.card-title {
  text-align: center;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 24px;
}
.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 260px;
  position: relative;

  .preview-field {
    white-space: nowrap;
    user-select: none;

    .qrcode-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;

      .qrcode-box {
        display: flex;
        align-items: center;
        justify-content: center;
        border: 2px dashed #d9d9d9;
        border-radius: 8px;
        padding: 8px;
        background: #fafafa;
        margin-bottom: 4px;
      }

      .qrcode-text {
        color: #666;
        white-space: nowrap;
      }
    }
  }
}
.student-name {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 24px;
  margin-top: 12px;
}
.certificate-title {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 8px;
}
.certificate-desc {
  font-size: 16px;
  color: #666;
  margin-bottom: 24px;
}
.issue-date {
  font-size: 16px;
  color: #222;
  margin-bottom: 12px;
  font-weight: bold;
}
.qrcode-field {
  position: absolute;
  right: 0;
  bottom: 0;
  font-size: 15px;
  color: #333;
  border: 1px dashed #bbb;
  border-radius: 4px;
  padding: 4px 10px;
  background: #f8f9fa;
}

/* HTML模板内容样式 */
.certificate-html-content {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 480px;
  padding: 20px;
}

.certificate-html-content .certificate-template {
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
}

/* 确保HTML模板中的文本可选择 */
.certificate-html-content * {
  user-select: text;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .certificate-html-content .certificate-template {
    transform: scale(0.8);
    transform-origin: center;
  }
}
</style>
