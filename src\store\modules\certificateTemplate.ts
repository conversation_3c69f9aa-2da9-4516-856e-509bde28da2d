import { defineStore } from 'pinia'
import { store } from '@/store'
import {
  CertificateTemplateApi,
  type CertificateTemplate,
  type CreateCertificateTemplateParams,
  type UpdateCertificateTemplateParams
} from '@/api/infra/certificateTemplate/index'
import { ElMessage } from 'element-plus'

interface CertificateTemplateState {
  // 当前编辑的模板
  currentTemplate: CertificateTemplate | null
  // 模板列表
  templateList: CertificateTemplate[]
  // 最近保存的模板ID
  lastSavedTemplateId: number | null
  // 加载状态
  loading: boolean
}

export const useCertificateTemplateStore = defineStore('certificateTemplate', {
  state: (): CertificateTemplateState => ({
    currentTemplate: null,
    templateList: [], // 初始化为空数组，通过API获取数据
    lastSavedTemplateId: null,
    loading: false
  }),

  getters: {
    // 获取当前模板
    getCurrentTemplate(): CertificateTemplate | null {
      return this.currentTemplate
    },

    // 根据ID获取模板
    getTemplateById:
      (state) =>
      (id: number): CertificateTemplate | null => {
        return state.templateList.find((template) => template.id === id) || null
      },

    // 获取最近保存的模板
    getLastSavedTemplate(): CertificateTemplate | null {
      if (!this.lastSavedTemplateId) return null
      return this.getTemplateById(this.lastSavedTemplateId)
    },

    // 获取模板列表
    getTemplateList(): CertificateTemplate[] {
      return this.templateList
    }
  },

  actions: {
    // 设置当前编辑的模板
    setCurrentTemplate(template: CertificateTemplate | null) {
      this.currentTemplate = template
    },

    // 获取模板列表
    async fetchTemplateList(): Promise<void> {
      this.loading = true
      try {
        const result = await CertificateTemplateApi.getPage({ page: 1, size: 1000 })
        this.templateList = result.list
      } catch (error) {
        console.error('获取证书模板列表失败:', error)
        ElMessage.error('获取模板列表失败')
      } finally {
        this.loading = false
      }
    },

    // 创建新模板
    async createTemplate(templateData: CreateCertificateTemplateParams): Promise<number | null> {
      this.loading = true
      try {
        const result = await CertificateTemplateApi.create(templateData)

        // 刷新列表
        await this.fetchTemplateList()

        // 设置为当前模板
        this.lastSavedTemplateId = result.id
        const newTemplate = this.getTemplateById(result.id)
        if (newTemplate) {
          this.currentTemplate = newTemplate
        }

        ElMessage.success('证书模板创建成功')
        return result.id
      } catch (error) {
        console.error('创建证书模板失败:', error)
        ElMessage.error('创建失败，请重试')
        return null
      } finally {
        this.loading = false
      }
    },

    // 更新模板
    async updateTemplate(templateData: UpdateCertificateTemplateParams): Promise<boolean> {
      this.loading = true
      try {
        await CertificateTemplateApi.update(templateData)

        // 刷新列表
        await this.fetchTemplateList()

        // 更新当前模板和最近保存的模板ID
        this.lastSavedTemplateId = templateData.id
        const updatedTemplate = this.getTemplateById(templateData.id)
        if (updatedTemplate) {
          this.currentTemplate = updatedTemplate
        }

        ElMessage.success('证书模板更新成功')
        return true
      } catch (error) {
        console.error('更新证书模板失败:', error)
        ElMessage.error('更新失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    },

    // 删除模板
    async deleteTemplate(id: number): Promise<boolean> {
      this.loading = true
      try {
        await CertificateTemplateApi.delete(id)

        // 刷新列表
        await this.fetchTemplateList()

        // 如果删除的是当前模板，清空当前模板
        if (this.currentTemplate?.id === id) {
          this.currentTemplate = null
        }

        // 如果删除的是最近保存的模板，清空记录
        if (this.lastSavedTemplateId === id) {
          this.lastSavedTemplateId = null
        }

        ElMessage.success('证书模板删除成功')
        return true
      } catch (error) {
        console.error('删除证书模板失败:', error)
        ElMessage.error('删除失败，请重试')
        return false
      } finally {
        this.loading = false
      }
    },

    // 获取模板详情
    async getTemplateDetail(id: number): Promise<CertificateTemplate | null> {
      this.loading = true
      try {
        const template = await CertificateTemplateApi.getDetail(id)
        this.currentTemplate = template
        return template
      } catch (error) {
        console.error('获取证书模板详情失败:', error)
        ElMessage.error('获取模板详情失败')
        return null
      } finally {
        this.loading = false
      }
    },

    // 清空当前模板
    clearCurrentTemplate() {
      this.currentTemplate = null
    },

    // 重置store
    resetStore() {
      this.currentTemplate = null
      this.templateList = []
      this.lastSavedTemplateId = null
      this.loading = false
    }
  },

  // 启用持久化
  persist: {
    key: 'certificate-template-store',
    storage: localStorage,
    paths: ['templateList', 'lastSavedTemplateId']
  }
})

// 在setup外使用
export const useCertificateTemplateStoreWithOut = () => {
  return useCertificateTemplateStore(store)
}
