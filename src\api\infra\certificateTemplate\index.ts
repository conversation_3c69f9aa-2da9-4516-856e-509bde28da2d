import request from '@/config/axios'

// 证书模板字段类型
export interface CertificateTemplateField {
  id: string
  field_type: string
  field_label: string
  position_x: number
  position_y: number
  font_size: number
  font_color: string
  font_family: string
  sort_order: number
}

// 证书模板数据结构
export interface CertificateTemplate {
  id?: number
  name: string
  type: string
  description?: string
  background?: string
  background_url?: string
  course?: string
  course_name?: string
  status: string
  html_content?: string
  preview_url?: string
  fields?: CertificateTemplateField[]
  creator?: string
  creator_name?: string
  create_time?: string
  updater?: string
  update_time?: string
}

// 分页查询参数
export interface CertificateTemplatePageParams {
  page?: number
  size?: number
  type?: string
  status?: string
  keyword?: string
}

// 分页查询结果
export interface CertificateTemplatePageResult {
  total: number
  list: CertificateTemplate[]
}

// 新增证书模板参数
export interface CreateCertificateTemplateParams {
  name: string
  type: string
  description?: string
  background?: string
  background_url?: string
  course?: string
  course_name?: string
  status: string
  html_content?: string
  fields?: CertificateTemplateField[]
}

// 更新证书模板参数
export interface UpdateCertificateTemplateParams extends CreateCertificateTemplateParams {
  id: number
}

// 证书模板统计结果
export interface CertificateTemplateStatisticsResult {
  total: number
  active_count: number
  inactive_count: number
  draft_count: number
}

// 证书模板列表项
export interface CertificateTemplateListItem {
  id: number
  name: string
  type: string
  status: string
}

// 新增证书模板响应结果
export interface CreateCertificateTemplateResult {
  id: number
}

// 证书模板API
export const CertificateTemplateApi = {
  // 分页查询证书模板
  getPage: async (
    params: CertificateTemplatePageParams
  ): Promise<CertificateTemplatePageResult> => {
    return await request.get({ url: '/publicbiz/certificate/template/page', params })
  },

  // 获取证书模板详情
  getDetail: async (id: number): Promise<CertificateTemplate> => {
    return await request.get({ url: '/publicbiz/certificate/template/detail', params: { id } })
  },

  // 新增证书模板
  create: async (data: CreateCertificateTemplateParams): Promise<CreateCertificateTemplateResult> => {
    return await request.post({ url: '/publicbiz/certificate/template/create', data })
  },

  // 更新证书模板
  update: async (data: UpdateCertificateTemplateParams): Promise<void> => {
    return await request.post({ url: '/publicbiz/certificate/template/update', data })
  },

  // 删除证书模板
  delete: async (id: number): Promise<void> => {
    return await request.post({ url: '/publicbiz/certificate/template/delete', data: { id } })
  },

  // 更新证书模板状态
  updateStatus: async (id: number, status: string): Promise<void> => {
    return await request.post({ 
      url: '/publicbiz/certificate/template/updateStatus', 
      data: { id, status } 
    })
  },

  // 获取证书模板统计报表
  getStatistics: async (): Promise<CertificateTemplateStatisticsResult> => {
    return await request.get({ url: '/publicbiz/certificate/template/statistics' })
  },

  // 获取证书模板列表（不分页）
  getList: async (status?: string): Promise<CertificateTemplateListItem[]> => {
    const params = status ? { status } : {}
    return await request.get({ url: '/publicbiz/certificate/template/list', params })
  }
}

// 字段类型映射（前端显示用）
export const fieldTypeMap: Record<string, string> = {
  name: '学员姓名',
  code: '证书编号',
  id: '身份证号',
  date: '发证日期',
  qrcode: '证书查验二维码'
}

// 证书类型映射
export const certificateTypeMap: Record<string, string> = {
  training: '培训证书',
  completion: '结业证书',
  skill: '技能证书'
}

// 模板状态映射
export const templateStatusMap: Record<string, string> = {
  draft: '草稿',
  active: '启用中',
  inactive: '已停用'
}

// 字体族选项
export const fontFamilyOptions = [
  { label: '微软雅黑', value: '微软雅黑' },
  { label: '黑体', value: '黑体' },
  { label: 'Arial', value: 'Arial' },
  { label: 'Times New Roman', value: 'Times New Roman' },
  { label: '宋体', value: '宋体' }
]

// 证书类型选项
export const certificateTypeOptions = [
  { label: '培训证书', value: 'training' },
  { label: '结业证书', value: 'completion' },
  { label: '技能证书', value: 'skill' }
]

// 模板状态选项
export const templateStatusOptions = [
  { label: '草稿', value: 'draft' },
  { label: '启用中', value: 'active' },
  { label: '已停用', value: 'inactive' }
]

// 占位符映射配置
export const placeholderMap: Record<string, string> = {
  name: 'studentName',
  code: 'certificateCode',
  id: 'studentId',
  date: 'issueDate',
  qrcode: 'qrcodeUrl'
}

// 默认占位符数据
export const defaultPlaceholderData: Record<string, string> = {
  studentName: '张三',
  certificateCode: 'CERT-2024-001',
  studentId: '123456789012345678',
  issueDate: '2024年8月1日',
  qrcodeUrl: 'https://example.com/qrcode/cert-2024-001.png'
}
